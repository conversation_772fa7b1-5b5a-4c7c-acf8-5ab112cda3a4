import sys
import json

# Import our custom DuckDB-NSQL-7B model implementation
from src.vanna.duckdb_nsql.duckdb_nsql_chat import DuckDBNSQL_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction
import os.path

import vanna
from vanna.flask import VannaFlaskApp

# Monkey patch to prevent kaleido import
sys.modules['kaleido'] = type('', (), {})()
sys.modules['kaleido.scopes'] = type('', (), {})() 
sys.modules['kaleido.scopes.plotly'] = type('', (), {'PlotlyScope': type('', (), {'render': lambda *args, **kwargs: b''})})

# Set environment variable for plotly
os.environ["PLOTLY_RENDERER"] = "svg"

# Create a custom Vanna class with DuckDB-NSQL-7B model and vector store
class MyVanna(ChromaDB_VectorStore, DuckDBNSQL_Chat):
    def __init__(self, config=None):
        if config is None:
            config = {}
        
        # Configure the DuckDB-NSQL-7B model
        config["temperature"] = 0.1  # Lower temperature for more deterministic SQL generation
        config["max_new_tokens"] = 1024  # Maximum tokens to generate
        
        # Configure ChromaDB vector store
        config["path"] = "./chromadb_store"  # Path to store the vector database
        
        # Initialize both parent classes
        ChromaDB_VectorStore.__init__(self, config=config)
        DuckDBNSQL_Chat.__init__(self, config=config)
    
    # Implement the required abstract methods
    def system_message(self, message):
        return {"role": "system", "content": message}
    
    def user_message(self, message):
        return {"role": "user", "content": message}
    
    def assistant_message(self, message):
        return {"role": "assistant", "content": message}
    
    def submit_prompt(self, prompt, **kwargs):
        """Submit a prompt to the DuckDB-NSQL model and get a response."""
        if not prompt:
            raise ValueError("Prompt is empty or None")
        
        # Format the prompt for the DuckDB-NSQL model
        formatted_prompt = ""
        for message in prompt:
            if isinstance(message, dict) and "role" in message and "content" in message:
                role = message["role"]
                content = message["content"]
                
                if role == "system":
                    formatted_prompt += f"### System:\n{content}\n\n"
                elif role == "user":
                    formatted_prompt += f"### User:\n{content}\n\n"
                elif role == "assistant":
                    formatted_prompt += f"### Assistant:\n{content}\n\n"
            else:
                # If it's just a string, treat it as user message
                formatted_prompt += f"### User:\n{message}\n\n"
        
        # Add a final assistant prompt
        formatted_prompt += "### Assistant:\n"
        
        # Generate response using the model's generate_response method
        return self.model.generate_response(
            formatted_prompt,
            max_new_tokens=kwargs.get("max_new_tokens", self.max_new_tokens),
            temperature=kwargs.get("temperature", self.temperature)
        )

# Create a singleton class for the embedding function to avoid reloading the model each time
class EmbeddingFunctionSingleton:
    _instance = None
    _model_path = './embedding_model_cache'
    _model_name = "intfloat/multilingual-e5-base"
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            print("Loading embedding model for the first time...")
            cls._instance = SentenceTransformerEmbeddingFunction(model_name=cls._model_name, cache_folder=cls._model_path)
            print("Embedding model loaded successfully.")
        return cls._instance

# Get the embedding function instance
ef = EmbeddingFunctionSingleton.get_instance()

# Create an instance of our custom Vanna class
vn = MyVanna(config={
    "embedding_function": ef,
    "n_results": 5  # Number of similar examples to retrieve
})

# Connect to a database (using PostgreSQL as an example)
vn.connect_to_postgres(
    host='************',
    dbname='clinical_db',
    user='nhri001',
    password='cbit2012',
    port=5432
)

# Define training database schema
training_schema_sql = """
CREATE TABLE public.users (
    user_id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.products (
    product_id SERIAL PRIMARY KEY,
    product_name VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    price DECIMAL(10, 2) NOT NULL,
    stock_quantity INTEGER DEFAULT 0
);

CREATE TABLE public.orders (
    order_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(user_id),
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    total_amount DECIMAL(12, 2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending'
);

CREATE TABLE public.order_items (
    item_id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(order_id),
    product_id INTEGER REFERENCES products(product_id),
    quantity INTEGER NOT NULL,
    price DECIMAL(10, 2) NOT NULL
);
"""

# Sample queries for training
sample_queries = [
    {
        "question": "Which users have placed the most orders?",
        "sql": """
SELECT u.username, COUNT(o.order_id) as order_count
FROM users u
JOIN orders o ON u.user_id = o.user_id
GROUP BY u.username
ORDER BY order_count DESC
LIMIT 5;
"""
    },
    {
        "question": "What is the average order amount by product category?",
        "sql": """
SELECT p.category, AVG(oi.price * oi.quantity) as avg_order_amount
FROM products p
JOIN order_items oi ON p.product_id = oi.product_id
GROUP BY p.category
ORDER BY avg_order_amount DESC;
"""
    },
    {
        "question": "Which products have never been ordered?",
        "sql": """
SELECT p.product_name
FROM products p
LEFT JOIN order_items oi ON p.product_id = oi.product_id
WHERE oi.item_id IS NULL;
"""
    },
    {
        "question": "What is the total revenue by month?",
        "sql": """
SELECT
    EXTRACT(YEAR FROM o.order_date) as year,
    EXTRACT(MONTH FROM o.order_date) as month,
    SUM(o.total_amount) as total_revenue
FROM orders o
GROUP BY year, month
ORDER BY year, month;
"""
    }
]

# Train the model with schema and sample queries
try:
    # First, create the schema
    vn.run_sql(training_schema_sql)
    print("Training schema created successfully")

    # Train with the schema DDL
    vn.train(ddl=training_schema_sql)
    print("Trained with schema DDL")

    # Train with sample queries
    for query in sample_queries:
        vn.train(question=query["question"], sql=query["sql"])
        print(f"Trained with query: {query['question']}")

    # Option to load training data from files
    use_file_based_training = True
    if use_file_based_training:
        # Load schema from file
        schema_file_path = 'training_data/postgres_sample/schema.sql'
        if os.path.exists(schema_file_path):
            with open(schema_file_path, 'r') as f:
                schema_sql = f.read()
                # Execute the schema SQL
                vn.run_sql(schema_sql)
                # Train with the schema
                vn.train(ddl=schema_sql)
                print(f"Loaded and trained with schema from {schema_file_path}")

        # Load questions from JSON file
        questions_file_path = 'training_data/postgres_sample/questions.json'
        if os.path.exists(questions_file_path):
            import json
            with open(questions_file_path, 'r') as f:
                questions_data = json.load(f)
                # Train with each question-answer pair
                for qa_pair in questions_data:
                    vn.train(question=qa_pair["question"], sql=qa_pair["answer"])
                print(f"Loaded and trained with {len(questions_data)} questions from {questions_file_path}")

except Exception as e:
    print(f"Error during training: {str(e)}")

# To list all collections
collections = vn.chroma_client.list_collections()
print(collections)  # This will show all collection names

# To get details about specific collections
sql_collection = vn.sql_collection
ddl_collection = vn.ddl_collection
documentation_collection = vn.documentation_collection

# To see the count of items in each collection
print(f"SQL Collection count: {sql_collection.count()}")
print(f"DDL Collection count: {ddl_collection.count()}")
print(f"Documentation Collection count: {documentation_collection.count()}")

# Test the DuckDB-NSQL-7B model with a sample question
print("\nTesting DuckDB-NSQL-7B model with a sample question:")
try:
    question = "What are the top 3 most expensive products?"
    sql = vn.generate_sql(question)
    print(f"Question: {question}")
    print(f"Generated SQL: {sql}")
    
    # Execute the generated SQL
    result_df = vn.run_sql(sql)
    print("\nQuery Result:")
    print(result_df.head())
    
    # Generate follow-up questions
    followup_questions = vn.generate_followup_questions(question, sql, result_df)
    print("\nFollow-up Questions:")
    for i, q in enumerate(followup_questions):
        print(f"{i+1}. {q}")
    
except Exception as e:
    print(f"Error testing the model: {str(e)}")

# Disable figure as image to avoid kaleido dependency
vanna.fig_as_img = False

# Start the Flask app
print("\nStarting Flask app...")
VannaFlaskApp(vn).run()
